"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import {
  Sparkles,
  Image as ImageIcon,
  Loader2,
  History,
  Download,
  Copy,
  Eye,
  Palette,
  Zap,
  Rocket,
  Star,
  Heart,
  Fire
} from "lucide-react";

interface GeneratedAd {
  id: string;
  type: string;
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  platform: string;
  format: string;
  media_assets?: Array<{
    id: string;
    type: string;
    url: string;
    storage_path: string;
    metadata: Record<string, any>;
  }>;
  generation_metadata?: Record<string, any>;
}

interface HistoryEntry {
  id: string;
  prompt: string;
  platforms: string[];
  imageStyle: string;
  adsCount: number;
  timestamp: string;
  ads: GeneratedAd[];
}

export default function PromptAdGeneratorPage() {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["facebook", "instagram", "linkedin", "google", "tiktok"]);
  const [imageStyle, setImageStyle] = useState("creative");
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [activeTab, setActiveTab] = useState("generate");

  const platforms = [
    { value: "facebook", label: "Facebook", icon: "📘", color: "bg-blue-600" },
    { value: "instagram", label: "Instagram", icon: "📷", color: "bg-pink-600" },
    { value: "google", label: "Google Ads", icon: "🔍", color: "bg-green-600" },
    { value: "linkedin", label: "LinkedIn", icon: "💼", color: "bg-blue-700" },
    { value: "twitter", label: "Twitter", icon: "🐦", color: "bg-blue-400" },
    { value: "tiktok", label: "TikTok", icon: "🎵", color: "bg-black" },
    { value: "youtube", label: "YouTube", icon: "📺", color: "bg-red-600" }
  ];

  const imageStyles = [
    { value: "creative", label: "Creative & Wild", icon: "🎨" },
    { value: "bold", label: "Bold & Striking", icon: "⚡" },
    { value: "minimalist", label: "Clean & Minimal", icon: "✨" },
    { value: "professional", label: "Professional", icon: "💼" }
  ];

  const promptExamples = [
    "Create ads for a revolutionary AI-powered fitness app that reads your mind",
    "Promote eco-friendly sneakers made from ocean plastic",
    "Sell premium coffee that gives you superpowers",
    "Market a dating app for introverts",
    "Advertise time-travel vacation packages"
  ];

  useEffect(() => {
    loadHistory();
  }, []);

  const generateAds = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt for your ad");
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast.error("Please select at least one platform");
      return;
    }

    setIsGenerating(true);

    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);
      const allGeneratedAds: GeneratedAd[] = [];
      
      // Generate ads for all selected platforms
      for (const platform of selectedPlatforms) {
        try {
          const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
          const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            },
            body: JSON.stringify({
              product_name: extractProductName(prompt),
              product_description: prompt,
              target_audience: "General audience",
              ad_type: "multimodal",
              platform: platform,
              tone: "creative",
              keywords: "",
              cta: "Learn More",
              additional_info: "Make it crazy and eye-catching!",
              num_variations: 2,
              // Always generate images for crazy visual ads
              generate_images: true,
              generate_videos: false,
              image_style: imageStyle,
              video_duration: 15,
              brand_colors: [],
              brand_fonts: [],
              use_brand_assets: true
            })
          });

          if (response.ok) {
            const data = await response.json();
            if (data.success && data.ads) {
              allGeneratedAds.push(...data.ads);
            }
          }
        } catch (error) {
          console.error(`Error generating ads for ${platform}:`, error);
        }
      }

      if (allGeneratedAds.length > 0) {
        setGeneratedAds(allGeneratedAds);
        toast.success(`🎉 Generated ${allGeneratedAds.length} crazy ads with images!`);
        
        // Add to history
        const historyEntry: HistoryEntry = {
          id: Date.now().toString(),
          prompt: prompt,
          platforms: selectedPlatforms,
          imageStyle: imageStyle,
          adsCount: allGeneratedAds.length,
          timestamp: new Date().toISOString(),
          ads: allGeneratedAds
        };
        setHistory(prev => [historyEntry, ...prev]);
        setActiveTab("results");
      } else {
        throw new Error("Failed to generate any ads");
      }
    } catch (error) {
      console.error("Error generating ads:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads");
    } finally {
      setIsGenerating(false);
    }
  };

  const extractProductName = (prompt: string): string => {
    const words = prompt.split(' ');
    if (words.length <= 3) return prompt;
    
    const productIndicators = ['for', 'about', 'selling', 'promoting'];
    for (const indicator of productIndicators) {
      const index = words.findIndex(word => word.toLowerCase().includes(indicator));
      if (index > 0) {
        return words.slice(0, index).join(' ');
      }
    }
    
    return words.slice(0, 3).join(' ');
  };

  const loadHistory = async () => {
    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) return;

      const token = await getIdToken(auth.currentUser);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${apiBaseUrl}/ad-generation/history?limit=20`, {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.history) {
          setHistory(data.history);
        }
      }
    } catch (error) {
      console.error("Error loading history:", error);
    }
  };

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platform)
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  const copyAdContent = (ad: GeneratedAd) => {
    const content = `${ad.headline}\n\n${ad.description}\n\n${ad.cta}`;
    navigator.clipboard.writeText(content);
    toast.success("Ad content copied to clipboard!");
  };

  const loadFromHistory = (entry: HistoryEntry) => {
    setPrompt(entry.prompt);
    setSelectedPlatforms(entry.platforms);
    setImageStyle(entry.imageStyle);
    setGeneratedAds(entry.ads);
    setActiveTab("results");
    toast.success("Loaded from history!");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Sparkles className="h-8 w-8 text-yellow-500" />
            AI Ad Generator
          </h1>
          <p className="text-muted-foreground">
            Create crazy, eye-catching ads with AI-generated images for all platforms
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => setShowHistory(!showHistory)}
          className="flex items-center gap-2"
        >
          <History className="h-4 w-4" />
          History ({history.length})
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="generate">Generate</TabsTrigger>
          <TabsTrigger value="results">Results ({generatedAds.length})</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          {/* Prompt Input */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                What do you want to advertise?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="Describe your product or service... Be creative! The crazier, the better!"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[100px] text-lg"
              />
              
              {/* Example prompts */}
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Need inspiration? Try these:</p>
                <div className="flex flex-wrap gap-2">
                  {promptExamples.map((example, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => setPrompt(example)}
                      className="text-xs"
                    >
                      {example.slice(0, 30)}...
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Platform Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Select Platforms</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {platforms.map((platform) => (
                  <div
                    key={platform.value}
                    className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                      selectedPlatforms.includes(platform.value)
                        ? "border-primary bg-primary/5"
                        : "border-gray-200"
                    }`}
                    onClick={() => handlePlatformToggle(platform.value)}
                  >
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedPlatforms.includes(platform.value)}
                        onChange={() => handlePlatformToggle(platform.value)}
                      />
                      <span className="text-lg">{platform.icon}</span>
                      <span className="text-sm font-medium">{platform.label}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Image Style */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Image Style
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {imageStyles.map((style) => (
                  <Button
                    key={style.value}
                    variant={imageStyle === style.value ? "default" : "outline"}
                    onClick={() => setImageStyle(style.value)}
                    className="flex items-center gap-2 h-auto p-4"
                  >
                    <span className="text-lg">{style.icon}</span>
                    <span className="text-sm">{style.label}</span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Generate Button */}
          <div className="flex justify-center">
            <Button
              onClick={generateAds}
              disabled={!prompt.trim() || selectedPlatforms.length === 0 || isGenerating}
              size="lg"
              className="min-w-[300px] h-14 text-lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-6 w-6 mr-2 animate-spin" />
                  Generating crazy ads...
                </>
              ) : (
                <>
                  <Rocket className="h-6 w-6 mr-2" />
                  Generate Crazy Ads with Images
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          {generatedAds.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {generatedAds.map((ad) => (
                <Card key={ad.id} className="overflow-hidden">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="capitalize">
                        {ad.platform}
                      </Badge>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyAdContent(ad)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {/* Generated Image */}
                    {ad.media_assets && ad.media_assets.length > 0 && (
                      <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                        <img
                          src={ad.media_assets[0].url}
                          alt={ad.headline || "Generated ad image"}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    
                    {/* Ad Content */}
                    <div className="space-y-2">
                      <h3 className="font-semibold text-lg">{ad.headline}</h3>
                      <p className="text-sm text-muted-foreground">{ad.description}</p>
                      <Badge variant="secondary">{ad.cta}</Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No ads generated yet</h3>
              <p className="text-muted-foreground">
                Go to the Generate tab to create your first crazy ad!
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          {history.length > 0 ? (
            <div className="space-y-4">
              {history.map((entry) => (
                <Card key={entry.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium line-clamp-2">{entry.prompt}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline">{entry.adsCount} ads</Badge>
                          <Badge variant="outline">{entry.platforms.length} platforms</Badge>
                          <Badge variant="outline">{entry.imageStyle}</Badge>
                          <span className="text-xs text-muted-foreground">
                            {new Date(entry.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadFromHistory(entry)}
                      >
                        Load
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No history yet</h3>
              <p className="text-muted-foreground">
                Your generated ads will appear here
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
