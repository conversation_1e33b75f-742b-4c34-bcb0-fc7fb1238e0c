"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Search,
  Filter,
  Download,
  Eye,
  Trash2,
  Calendar,
  Image as ImageIcon,
  Video,
  FileText,
  Share2,
  Clock,
  CheckCircle,
  XCircle,
  Loader2
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface MediaAsset {
  id: string;
  type: string;
  url: string;
  storage_path: string;
  metadata: Record<string, any>;
  created_at: string;
  size_bytes?: number;
  dimensions?: { width: number; height: number };
}

interface GeneratedAd {
  id: string;
  type: string;
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  platform: string;
  format: string;
  media_assets: MediaAsset[];
  generation_metadata: Record<string, any>;
  platform_adaptations: Record<string, any>;
}

interface AdGenerationSession {
  session_id: string;
  brand_id: string;
  request: any;
  generated_ads: GeneratedAd[];
  status: string;
  progress: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
  summary: {
    num_ads: number;
    has_images: boolean;
    platforms: string[];
  };
}

export default function AdGenerationHistoryPage() {
  const [sessions, setSessions] = useState<AdGenerationSession[]>([]);
  const [filteredSessions, setFilteredSessions] = useState<AdGenerationSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedSession, setSelectedSession] = useState<AdGenerationSession | null>(null);

  useEffect(() => {
    fetchHistory();
  }, []);

  useEffect(() => {
    filterSessions();
  }, [sessions, searchTerm, statusFilter]);

  const fetchHistory = async () => {
    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to view history");
        return;
      }

      const token = await getIdToken(auth.currentUser);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/ad-generation/history?limit=50`, {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to fetch history");
      }

      const data = await response.json();
      setSessions(data.history || []);
    } catch (error) {
      console.error("Error fetching history:", error);
      toast.error("Failed to load ad generation history");
    } finally {
      setLoading(false);
    }
  };

  const filterSessions = () => {
    let filtered = sessions;

    if (searchTerm) {
      filtered = filtered.filter(session =>
        session.request?.product_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        session.request?.product_description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(session => session.status === statusFilter);
    }

    setFilteredSessions(filtered);
  };

  const deleteSession = async (sessionId: string) => {
    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to delete sessions");
        return;
      }

      const token = await getIdToken(auth.currentUser);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/ad-generation/session/${sessionId}`, {
        method: "DELETE",
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to delete session");
      }

      setSessions(sessions.filter(s => s.session_id !== sessionId));
      toast.success("Session deleted successfully");
    } catch (error) {
      console.error("Error deleting session:", error);
      toast.error("Failed to delete session");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "processing":
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAdTypeIcon = (type: string) => {
    switch (type) {
      case "image":
        return <ImageIcon className="h-4 w-4" />;
      case "video":
        return <Video className="h-4 w-4" />;
      case "multimodal":
        return <Share2 className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Ad Generation History</h1>
          <p className="text-muted-foreground">
            View and manage your generated ads and sessions
          </p>
        </div>
        <Button onClick={fetchHistory} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by product name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="processing">Processing</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Sessions Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredSessions.map((session) => (
          <Card key={session.session_id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(session.status)}
                  <Badge variant={session.status === "completed" ? "default" : "secondary"}>
                    {session.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-1">
                  {getAdTypeIcon(session.request?.ad_type)}
                  <span className="text-sm text-muted-foreground">
                    {session.request?.ad_type}
                  </span>
                </div>
              </div>
              <CardTitle className="text-lg line-clamp-2">
                {session.request?.product_name || "Untitled"}
              </CardTitle>
              <p className="text-sm text-muted-foreground line-clamp-2">
                {session.request?.product_description}
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Generated:</span>
                  <span>{session.summary?.num_ads || 0} ads</span>
                </div>
                
                {session.summary?.has_images && (
                  <div className="flex items-center gap-2 text-sm">
                    <ImageIcon className="h-3 w-3" />
                    <span className="text-muted-foreground">Includes media</span>
                  </div>
                )}

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Platform:</span>
                  <Badge variant="outline">{session.request?.platform}</Badge>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{formatDistanceToNow(new Date(session.created_at))} ago</span>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedSession(session)}
                    className="flex-1"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    View
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => deleteSession(session.session_id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredSessions.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No ad generation sessions found</h3>
          <p className="text-muted-foreground">
            {searchTerm || statusFilter !== "all" 
              ? "Try adjusting your filters" 
              : "Start generating ads to see your history here"}
          </p>
        </div>
      )}
    </div>
  );
}
