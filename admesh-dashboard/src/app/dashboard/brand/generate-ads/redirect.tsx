"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Sparkles,
  Rocket,
  Zap,
  ArrowRight,
  ImageIcon,
  Target,
  History
} from "lucide-react";

export default function GenerateAdsRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Auto-redirect after 3 seconds
    const timer = setTimeout(() => {
      router.push("/dashboard/brand/generate-ads/prompt");
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center">
          <Sparkles className="h-16 w-16 text-yellow-500 animate-pulse" />
        </div>
        <h1 className="text-4xl font-bold">🎉 New AI Ad Generator!</h1>
        <p className="text-xl text-muted-foreground">
          We've upgraded to a crazy new prompt-based ad generator with AI images!
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card className="text-center">
          <CardHeader>
            <Zap className="h-12 w-12 text-yellow-500 mx-auto" />
            <CardTitle>Simple Prompts</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Just type what you want to advertise - no complex forms!
            </p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <ImageIcon className="h-12 w-12 text-blue-500 mx-auto" />
            <CardTitle>AI Images</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Automatically generates crazy, eye-catching images for every ad
            </p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Target className="h-12 w-12 text-green-500 mx-auto" />
            <CardTitle>All Platforms</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Creates optimized ads for Facebook, Instagram, LinkedIn, and more
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="text-center space-y-4">
        <p className="text-muted-foreground">Redirecting automatically in 3 seconds...</p>
        <Button
          onClick={() => router.push("/dashboard/brand/generate-ads/prompt")}
          size="lg"
          className="min-w-[250px]"
        >
          <Rocket className="h-5 w-5 mr-2" />
          Try New Generator Now
          <ArrowRight className="h-5 w-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
