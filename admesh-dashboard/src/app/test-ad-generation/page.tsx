"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";

export default function TestAdGenerationPage() {
  const [prompt, setPrompt] = useState("Create ads for a revolutionary AI-powered fitness app");
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testGeneration = async () => {
    setIsGenerating(true);
    setResult(null);

    try {
      // Test without authentication first
      const apiBaseUrl = 'http://127.0.0.1:8000';
      
      const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // Skip auth for testing
        },
        body: JSON.stringify({
          product_name: "AI Fitness App",
          product_description: prompt,
          target_audience: "Fitness enthusiasts",
          ad_type: "multimodal",
          platform: "facebook",
          tone: "creative",
          keywords: "fitness, AI, health",
          cta: "Download Now",
          additional_info: "Make it exciting!",
          num_variations: 1,
          generate_images: true,
          generate_videos: false,
          image_style: "creative",
          use_brand_assets: false
        })
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      const data = await response.text();
      console.log('Response data:', data);

      if (response.ok) {
        const jsonData = JSON.parse(data);
        setResult(jsonData);
        toast.success("Test successful!");
      } else {
        setResult({ error: data, status: response.status });
        toast.error(`Test failed: ${response.status}`);
      }
    } catch (error) {
      console.error("Test error:", error);
      setResult({ error: error.message });
      toast.error(`Network error: ${error.message}`);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Test Ad Generation API</h1>
        <p className="text-muted-foreground">
          Test the new ad generation endpoint without authentication
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Test Parameters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Prompt:</label>
            <Input
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter your ad prompt..."
            />
          </div>
          
          <Button
            onClick={testGeneration}
            disabled={isGenerating}
            className="w-full"
          >
            {isGenerating ? "Testing..." : "Test Ad Generation"}
          </Button>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle>Result</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>API Status Check</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>✅ API Server: Running on http://127.0.0.1:8000</p>
            <p>✅ Documentation: Available at http://127.0.0.1:8000/docs</p>
            <p>🔧 Testing endpoint: /ad-generation/generate</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
